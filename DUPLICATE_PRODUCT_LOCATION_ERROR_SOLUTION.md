# Solution for "You cannot add the same product with the same location" Error

## Problem Description

You're encountering the validation error:
```
You cannot add the same product VARIYALI READY DIAMOND 1% [30KG] with the same location 'Stock'
```

This error typically occurs when trying to create duplicate stock entries for the same product in the same location.

## Root Causes

1. **Existing Stock Quant**: There's already a stock quant record for this product-location combination
2. **Pending Inventory Adjustment**: There's an unfinished inventory adjustment for this product
3. **Duplicate Stock Moves**: Multiple stock moves are trying to create quants for the same product-location
4. **Database Constraint**: A unique constraint prevents duplicate entries
5. **Custom Validation**: Custom code is preventing the operation

## Immediate Solutions

### Solution 1: Check and Apply Pending Inventory Adjustments

```python
# Run in Odoo shell
product = env['product.product'].search([('name', '=', 'VARIYALI READY DIAMOND 1% [30KG]')])
stock_location = env['stock.location'].search([('name', '=', 'Stock')])

# Check for pending adjustments
pending_adjustments = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('location_id', '=', stock_location.id),
    ('inventory_quantity_set', '=', True)
])

# Apply or reset adjustments
for adj in pending_adjustments:
    adj.action_apply_inventory()  # or adj.inventory_quantity_set = False
```

### Solution 2: Update Existing Quant Instead of Creating New

```python
# Run in Odoo shell
existing_quant = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('location_id', '=', stock_location.id)
], limit=1)

if existing_quant:
    # Update existing quantity
    new_quantity = 100.0  # Your desired quantity
    existing_quant.inventory_quantity = new_quantity
    existing_quant.action_apply_inventory()
else:
    # Create new quant
    env['stock.quant'].create({
        'product_id': product.id,
        'location_id': stock_location.id,
        'quantity': new_quantity,
    })
```

### Solution 3: Use Safe Create Method

```python
# Use the safe method from the custom module
env['stock.quant'].safe_create_or_update(
    product_id=product.id,
    location_id=stock_location.id,
    quantity=100.0
)
```

## Long-term Solutions

### Install the Stock Duplicate Prevention Module

1. Copy the `stock_duplicate_prevention` folder to your addons directory
2. Update the module list: `Settings > Apps > Update Apps List`
3. Install the module: Search for "Stock Duplicate Prevention" and install

### Features of the Prevention Module

- **Enhanced Error Messages**: Better error descriptions with suggestions
- **Safe Create Methods**: Methods that handle duplicates automatically
- **Duplicate Resolver Wizard**: GUI tool to resolve conflicts
- **Automatic Conflict Resolution**: Handles duplicates during stock moves

## Investigation Steps

### Step 1: Identify the Source

Run the debug script:
```bash
python debug_validation_error.py
```

### Step 2: Check Existing Records

```python
# In Odoo shell
product = env['product.product'].search([('name', '=', 'VARIYALI READY DIAMOND 1% [30KG]')])
quants = env['stock.quant'].search([('product_id', '=', product.id)])

for quant in quants:
    print(f"Location: {quant.location_id.name}, Qty: {quant.quantity}, Reserved: {quant.reserved_quantity}")
```

### Step 3: Check Pending Operations

```python
# Check pending moves
moves = env['stock.move'].search([
    ('product_id', '=', product.id),
    ('state', 'not in', ['done', 'cancel'])
])

for move in moves:
    print(f"Move: {move.name}, State: {move.state}, To: {move.location_dest_id.name}")
```

## Prevention Best Practices

1. **Always Check Before Creating**: Use `check_duplicate_risk()` method
2. **Use Safe Methods**: Use `safe_create_or_update()` instead of direct `create()`
3. **Handle Exceptions**: Wrap stock operations in try-catch blocks
4. **Validate Data**: Check for duplicates in import data
5. **Use Inventory Adjustments**: For quantity changes, use inventory adjustments

## API Reference

### StockQuant Methods

```python
# Check for duplicate risk
result = env['stock.quant'].check_duplicate_risk(product_id, location_id, lot_id)

# Safe create or update
quant = env['stock.quant'].safe_create_or_update(product_id, location_id, quantity, lot_id)

# Resolve duplicates automatically
quant = env['stock.duplicate.resolver'].resolve_duplicate_automatically(
    product_id, location_id, quantity, lot_id
)
```

## Troubleshooting

### If the error persists:

1. Check for custom constraints in your modules
2. Look for SQL constraints in the database
3. Verify that you're not in a transaction that's causing conflicts
4. Check if there are any custom validations in inherited models

### Common Mistakes:

- Trying to create quants directly instead of using stock moves
- Not checking for existing quants before creating new ones
- Mixing inventory adjustments with direct quant creation
- Not handling lot/serial number tracking properly

## Support

If you continue to experience issues:

1. Enable debug mode and check the full error traceback
2. Check the server logs for more detailed error information
3. Use the investigation script to gather more information
4. Consider using the duplicate resolver wizard for manual resolution

## Files Created

- `debug_validation_error.py` - Investigation script
- `fix_duplicate_product_location_error.py` - Solution examples
- `stock_duplicate_prevention/` - Prevention module
- This README file

Run these tools to diagnose and fix the duplicate product-location error.
