from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    @api.model
    def safe_create_or_update(self, product_id, location_id, quantity, lot_id=None):
        """
        Safely create or update a stock quant without causing duplicate errors.
        
        Args:
            product_id (int): Product ID
            location_id (int): Location ID  
            quantity (float): Quantity to set
            lot_id (int, optional): Lot ID
            
        Returns:
            stock.quant: The created or updated quant record
        """
        domain = [
            ('product_id', '=', product_id),
            ('location_id', '=', location_id),
        ]
        
        if lot_id:
            domain.append(('lot_id', '=', lot_id))
        else:
            domain.append(('lot_id', '=', False))
            
        existing_quant = self.search(domain, limit=1)
        
        if existing_quant:
            # Update existing quant using inventory adjustment
            _logger.info(f"Updating existing quant {existing_quant.id} with quantity {quantity}")
            existing_quant.inventory_quantity = quantity
            if hasattr(existing_quant, 'action_apply_inventory'):
                existing_quant.action_apply_inventory()
            return existing_quant
        else:
            # Create new quant
            _logger.info(f"Creating new quant for product {product_id} in location {location_id}")
            vals = {
                'product_id': product_id,
                'location_id': location_id,
                'quantity': quantity,
            }
            if lot_id:
                vals['lot_id'] = lot_id
                
            return self.create(vals)

    @api.model
    def check_duplicate_risk(self, product_id, location_id, lot_id=None):
        """
        Check if creating a quant would cause a duplicate error.
        
        Returns:
            dict: {'has_duplicate': bool, 'existing_quant': record or False, 'message': str}
        """
        domain = [
            ('product_id', '=', product_id),
            ('location_id', '=', location_id),
        ]
        
        if lot_id:
            domain.append(('lot_id', '=', lot_id))
        else:
            domain.append(('lot_id', '=', False))
            
        existing_quant = self.search(domain, limit=1)
        
        if existing_quant:
            product = self.env['product.product'].browse(product_id)
            location = self.env['stock.location'].browse(location_id)
            
            message = _(
                "A stock quant already exists for product '%(product)s' "
                "in location '%(location)s'. Current quantity: %(quantity)s"
            ) % {
                'product': product.name,
                'location': location.name,
                'quantity': existing_quant.quantity
            }
            
            return {
                'has_duplicate': True,
                'existing_quant': existing_quant,
                'message': message
            }
        
        return {
            'has_duplicate': False,
            'existing_quant': False,
            'message': _("No duplicate risk detected")
        }

    @api.model
    def create(self, vals):
        """
        Override create to provide better error messages for duplicates.
        """
        try:
            return super(StockQuant, self).create(vals)
        except Exception as e:
            error_msg = str(e)
            
            # Check if this is a duplicate product-location error
            if ('same product' in error_msg.lower() and 
                'same location' in error_msg.lower()):
                
                product_id = vals.get('product_id')
                location_id = vals.get('location_id')
                
                if product_id and location_id:
                    duplicate_info = self.check_duplicate_risk(
                        product_id, location_id, vals.get('lot_id')
                    )
                    
                    if duplicate_info['has_duplicate']:
                        enhanced_message = _(
                            "Cannot create duplicate stock quant.\n\n"
                            "%(original_message)s\n\n"
                            "Suggestion: Use the 'Update Existing Quant' action "
                            "or inventory adjustment instead."
                        ) % {'original_message': duplicate_info['message']}
                        
                        raise ValidationError(enhanced_message)
            
            # Re-raise the original error if it's not a duplicate issue
            raise

    def action_open_duplicate_resolver(self):
        """
        Open a wizard to resolve duplicate quant issues.
        """
        return {
            'type': 'ir.actions.act_window',
            'name': _('Resolve Duplicate Stock Quant'),
            'res_model': 'stock.duplicate.resolver',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_product_id': self.product_id.id,
                'default_location_id': self.location_id.id,
                'default_lot_id': self.lot_id.id if self.lot_id else False,
                'default_current_quantity': self.quantity,
            }
        }
