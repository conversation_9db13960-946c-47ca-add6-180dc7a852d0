<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced Stock Quant Tree View -->
    <record id="view_stock_quant_tree_enhanced" model="ir.ui.view">
        <field name="name">stock.quant.tree.enhanced</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="decoration-warning">quantity &lt; 0</attribute>
                <attribute name="decoration-info">inventory_quantity_set</attribute>
            </xpath>
        </field>
    </record>

    <!-- Enhanced Stock Quant Form View -->
    <record id="view_stock_quant_form_enhanced" model="ir.ui.view">
        <field name="name">stock.quant.form.enhanced</field>
        <field name="model">stock.quant</field>
        <field name="inherit_id" ref="stock.view_stock_quant_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="action_open_duplicate_resolver" 
                        type="object" 
                        string="Resolve Duplicates" 
                        class="btn-secondary"
                        help="Open wizard to resolve duplicate quant issues"/>
            </xpath>
        </field>
    </record>

    <!-- Action for Stock Duplicate Resolver -->
    <record id="action_stock_duplicate_resolver" model="ir.actions.act_window">
        <field name="name">Resolve Stock Duplicates</field>
        <field name="res_model">stock.duplicate.resolver</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu item for Stock Duplicate Resolver -->
    <menuitem id="menu_stock_duplicate_resolver"
              name="Resolve Duplicates"
              parent="stock.menu_stock_inventory_control"
              action="action_stock_duplicate_resolver"
              sequence="50"/>
</odoo>
