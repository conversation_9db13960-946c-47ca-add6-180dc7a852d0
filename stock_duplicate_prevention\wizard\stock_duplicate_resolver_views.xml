<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Stock Duplicate Resolver Form View -->
    <record id="view_stock_duplicate_resolver_form" model="ir.ui.view">
        <field name="name">stock.duplicate.resolver.form</field>
        <field name="model">stock.duplicate.resolver</field>
        <field name="arch" type="xml">
            <form string="Resolve Stock Duplicate">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="product_id" readonly="1"/>
                        </h1>
                        <h2>
                            Location: <field name="location_id" readonly="1"/>
                        </h2>
                    </div>
                    
                    <group>
                        <group>
                            <field name="lot_id" readonly="1"/>
                            <field name="current_quantity" readonly="1"/>
                        </group>
                        <group>
                            <field name="operation"/>
                            <field name="new_quantity"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Existing Quants">
                            <field name="existing_quant_ids" readonly="1">
                                <tree>
                                    <field name="product_id"/>
                                    <field name="location_id"/>
                                    <field name="lot_id"/>
                                    <field name="quantity"/>
                                    <field name="reserved_quantity"/>
                                    <field name="inventory_quantity_set"/>
                                </tree>
                            </field>
                            <div class="oe_clear">
                                <button name="action_view_existing_quants" 
                                        type="object" 
                                        string="View All Existing Quants" 
                                        class="btn-link"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                
                <footer>
                    <button name="action_resolve_duplicate" 
                            type="object" 
                            string="Resolve Duplicate" 
                            class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>
