{
    'name': 'Stock Duplicate Prevention',
    'version': '********.0',
    'category': 'Inventory',
    'summary': 'Prevent and handle duplicate product-location validation errors',
    'description': """
        This module helps prevent and handle validation errors related to
        duplicate product-location combinations in stock operations.
        
        Features:
        - Enhanced error messages with suggestions
        - Helper methods for safe stock operations
        - Debugging tools for stock issues
        - Prevention of common duplicate scenarios
    """,
    'author': 'Your Company',
    'depends': ['stock'],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_quant_views.xml',
        'wizard/stock_duplicate_resolver_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
