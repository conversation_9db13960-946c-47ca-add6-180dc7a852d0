from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    _inherit = 'stock.move'

    def _action_done(self, cancel_backorder=False):
        """
        Override to handle potential duplicate quant creation during move completion.
        """
        try:
            return super(<PERSON><PERSON><PERSON>, self)._action_done(cancel_backorder=cancel_backorder)
        except Exception as e:
            error_msg = str(e)
            
            # Check if this is a duplicate product-location error
            if ('same product' in error_msg.lower() and 
                'same location' in error_msg.lower()):
                
                _logger.warning(f"Duplicate quant error during move completion: {error_msg}")
                
                # Try to resolve by updating existing quants
                for move in self:
                    if move.state != 'done':
                        self._resolve_duplicate_quants(move)
                
                # Retry the operation
                return super(StockM<PERSON>, self)._action_done(cancel_backorder=cancel_backorder)
            
            # Re-raise the original error if it's not a duplicate issue
            raise

    def _resolve_duplicate_quants(self, move):
        """
        Resolve duplicate quant issues for a specific move.
        """
        _logger.info(f"Attempting to resolve duplicate quants for move {move.name}")
        
        # Check if there are existing quants in the destination location
        existing_quants = self.env['stock.quant'].search([
            ('product_id', '=', move.product_id.id),
            ('location_id', '=', move.location_dest_id.id),
        ])
        
        if existing_quants:
            # Update the existing quant instead of creating a new one
            total_quantity = sum(existing_quants.mapped('quantity')) + move.product_uom_qty
            
            # Keep the first quant and merge others into it
            main_quant = existing_quants[0]
            other_quants = existing_quants[1:]
            
            # Update the main quant
            main_quant.quantity = total_quantity
            
            # Remove other quants
            if other_quants:
                other_quants.unlink()
                
            _logger.info(f"Merged quants for product {move.product_id.name} in {move.location_dest_id.name}")


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    @api.model
    def create(self, vals):
        """
        Override create to prevent duplicate move line issues.
        """
        try:
            return super(StockMoveLine, self).create(vals)
        except Exception as e:
            error_msg = str(e)
            
            if ('same product' in error_msg.lower() and 
                'same location' in error_msg.lower()):
                
                _logger.warning(f"Duplicate error in move line creation: {error_msg}")
                
                # Check for existing move lines with same product/location
                product_id = vals.get('product_id')
                location_dest_id = vals.get('location_dest_id')
                move_id = vals.get('move_id')
                
                if product_id and location_dest_id and move_id:
                    existing_lines = self.search([
                        ('product_id', '=', product_id),
                        ('location_dest_id', '=', location_dest_id),
                        ('move_id', '=', move_id),
                        ('state', '!=', 'done')
                    ])
                    
                    if existing_lines:
                        # Update existing line instead of creating new
                        existing_line = existing_lines[0]
                        existing_line.product_uom_qty += vals.get('product_uom_qty', 0)
                        existing_line.qty_done += vals.get('qty_done', 0)
                        
                        _logger.info(f"Updated existing move line {existing_line.id} instead of creating duplicate")
                        return existing_line
            
            # Re-raise the original error if it's not a duplicate issue
            raise
