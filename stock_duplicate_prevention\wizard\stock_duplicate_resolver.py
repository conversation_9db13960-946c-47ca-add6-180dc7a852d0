from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class StockDuplicateResolver(models.TransientModel):
    _name = 'stock.duplicate.resolver'
    _description = 'Stock Duplicate Resolver Wizard'

    product_id = fields.Many2one('product.product', string='Product', required=True)
    location_id = fields.Many2one('stock.location', string='Location', required=True)
    lot_id = fields.Many2one('stock.lot', string='Lot/Serial Number')
    current_quantity = fields.Float(string='Current Quantity', readonly=True)
    new_quantity = fields.Float(string='New Quantity', required=True)
    operation = fields.Selection([
        ('replace', 'Replace Current Quantity'),
        ('add', 'Add to Current Quantity'),
        ('subtract', 'Subtract from Current Quantity'),
    ], string='Operation', default='replace', required=True)
    
    existing_quant_ids = fields.One2many(
        'stock.quant', 
        compute='_compute_existing_quants',
        string='Existing Quants'
    )

    @api.depends('product_id', 'location_id', 'lot_id')
    def _compute_existing_quants(self):
        """Compute existing quants for the product-location combination."""
        for wizard in self:
            domain = [
                ('product_id', '=', wizard.product_id.id),
                ('location_id', '=', wizard.location_id.id),
            ]
            
            if wizard.lot_id:
                domain.append(('lot_id', '=', wizard.lot_id.id))
            else:
                domain.append(('lot_id', '=', False))
                
            wizard.existing_quant_ids = self.env['stock.quant'].search(domain)

    def action_resolve_duplicate(self):
        """Resolve the duplicate quant issue based on selected operation."""
        self.ensure_one()
        
        existing_quant = self.existing_quant_ids[:1]  # Get first quant
        
        if not existing_quant:
            # No existing quant, create new one
            vals = {
                'product_id': self.product_id.id,
                'location_id': self.location_id.id,
                'quantity': self.new_quantity,
            }
            if self.lot_id:
                vals['lot_id'] = self.lot_id.id
                
            self.env['stock.quant'].create(vals)
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('New stock quant created successfully.'),
                    'type': 'success',
                }
            }
        
        # Calculate final quantity based on operation
        if self.operation == 'replace':
            final_quantity = self.new_quantity
        elif self.operation == 'add':
            final_quantity = existing_quant.quantity + self.new_quantity
        elif self.operation == 'subtract':
            final_quantity = existing_quant.quantity - self.new_quantity
            if final_quantity < 0:
                raise UserError(_('Final quantity cannot be negative.'))
        
        # Update the existing quant using inventory adjustment
        existing_quant.inventory_quantity = final_quantity
        
        if hasattr(existing_quant, 'action_apply_inventory'):
            existing_quant.action_apply_inventory()
        
        # If there are multiple quants, merge them
        if len(self.existing_quant_ids) > 1:
            other_quants = self.existing_quant_ids[1:]
            total_other_quantity = sum(other_quants.mapped('quantity'))
            
            existing_quant.quantity += total_other_quantity
            other_quants.unlink()
            
            _logger.info(f"Merged {len(other_quants)} duplicate quants")
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Stock quant updated successfully. Final quantity: %s') % final_quantity,
                'type': 'success',
            }
        }

    def action_view_existing_quants(self):
        """Open a view showing all existing quants for this product-location."""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Existing Stock Quants'),
            'res_model': 'stock.quant',
            'view_mode': 'tree,form',
            'domain': [('id', 'in', self.existing_quant_ids.ids)],
            'context': {'create': False},
        }

    @api.model
    def resolve_duplicate_automatically(self, product_id, location_id, quantity, lot_id=None):
        """
        Automatically resolve duplicate quant issues.
        This method can be called from other parts of the system.
        """
        domain = [
            ('product_id', '=', product_id),
            ('location_id', '=', location_id),
        ]
        
        if lot_id:
            domain.append(('lot_id', '=', lot_id))
        else:
            domain.append(('lot_id', '=', False))
            
        existing_quants = self.env['stock.quant'].search(domain)
        
        if existing_quants:
            # Merge all quants into the first one
            main_quant = existing_quants[0]
            other_quants = existing_quants[1:]
            
            total_quantity = sum(existing_quants.mapped('quantity')) + quantity
            
            main_quant.inventory_quantity = total_quantity
            if hasattr(main_quant, 'action_apply_inventory'):
                main_quant.action_apply_inventory()
            
            if other_quants:
                other_quants.unlink()
                
            _logger.info(f"Automatically resolved duplicate quants for product {product_id}")
            return main_quant
        else:
            # Create new quant
            vals = {
                'product_id': product_id,
                'location_id': location_id,
                'quantity': quantity,
            }
            if lot_id:
                vals['lot_id'] = lot_id
                
            return self.env['stock.quant'].create(vals)
