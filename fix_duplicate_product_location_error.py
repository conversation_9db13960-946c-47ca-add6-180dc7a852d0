#!/usr/bin/env python3
"""
Solutions for fixing the validation error:
"You cannot add the same product VARIYALI READY DIAMOND 1% [30KG] with the same location 'Stock'"

This script provides various solutions based on different scenarios.
"""

def solution_1_inventory_adjustment():
    """
    Solution 1: If the error occurs during inventory adjustment
    """
    print("=== SOLUTION 1: INVENTORY ADJUSTMENT ISSUE ===")
    print()
    print("If you're trying to do an inventory adjustment and getting this error:")
    print()
    
    odoo_shell_code = '''
# Run in Odoo shell to fix inventory adjustment issue

# 1. Find the product and location
product = env['product.product'].search([('name', '=', 'VARIYALI READY DIAMOND 1% [30KG]')])
stock_location = env['stock.location'].search([('name', '=', 'Stock')])

# 2. Check for existing inventory adjustments
existing_adjustments = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('location_id', '=', stock_location.id),
    ('inventory_quantity_set', '=', True)
])

print(f"Found {len(existing_adjustments)} pending inventory adjustments")

# 3. If there are pending adjustments, apply or cancel them first
for adj in existing_adjustments:
    print(f"Adjustment: Current={adj.quantity}, Set to={adj.inventory_quantity}")
    # Option A: Apply the adjustment
    adj.action_apply_inventory()
    # Option B: Reset the adjustment
    # adj.inventory_quantity_set = False

# 4. Now try your inventory adjustment again
'''
    
    print(odoo_shell_code)

def solution_2_update_existing_quant():
    """
    Solution 2: Update existing quant instead of creating new
    """
    print("=== SOLUTION 2: UPDATE EXISTING QUANT ===")
    print()
    print("If you need to add quantity to existing stock:")
    print()
    
    odoo_shell_code = '''
# Run in Odoo shell to update existing quant

# 1. Find the product and location
product = env['product.product'].search([('name', '=', 'VARIYALI READY DIAMOND 1% [30KG]')])
stock_location = env['stock.location'].search([('name', '=', 'Stock')])

# 2. Use the proper method to update quantity
quantity_to_add = 10.0  # Change this to your desired quantity

# Method A: Use _update_available_quantity (recommended)
env['stock.quant']._update_available_quantity(
    product, 
    stock_location, 
    quantity_to_add
)

# Method B: Use inventory adjustment
quant = env['stock.quant'].search([
    ('product_id', '=', product.id),
    ('location_id', '=', stock_location.id)
], limit=1)

if quant:
    quant.inventory_quantity = quant.quantity + quantity_to_add
    quant.action_apply_inventory()
else:
    # Create new quant if none exists
    env['stock.quant'].create({
        'product_id': product.id,
        'location_id': stock_location.id,
        'quantity': quantity_to_add,
    })
'''
    
    print(odoo_shell_code)

def solution_3_check_constraints():
    """
    Solution 3: Check and handle custom constraints
    """
    print("=== SOLUTION 3: CHECK CUSTOM CONSTRAINTS ===")
    print()
    print("If there's a custom constraint causing the issue:")
    print()
    
    odoo_shell_code = '''
# Run in Odoo shell to check constraints

# 1. Check all models for SQL constraints related to product/location
models_to_check = ['stock.quant', 'stock.move', 'stock.move.line']

for model_name in models_to_check:
    model = env[model_name]
    if hasattr(model, '_sql_constraints'):
        print(f"\\n{model_name} constraints:")
        for constraint in model._sql_constraints:
            print(f"  {constraint}")

# 2. Check for any custom validation methods
import inspect

for model_name in models_to_check:
    model = env[model_name]
    methods = inspect.getmembers(model.__class__, predicate=inspect.isfunction)
    constraint_methods = [m for m in methods if m[0].startswith('_check_') or 'constrain' in m[0]]
    if constraint_methods:
        print(f"\\n{model_name} constraint methods:")
        for method_name, method in constraint_methods:
            print(f"  {method_name}")
'''
    
    print(odoo_shell_code)

def solution_4_data_import_fix():
    """
    Solution 4: Fix data import issues
    """
    print("=== SOLUTION 4: DATA IMPORT FIX ===")
    print()
    print("If you're importing data and getting duplicates:")
    print()
    
    python_code = '''
# Python code for safe data import

def safe_create_or_update_quant(env, product_name, location_name, quantity):
    """
    Safely create or update stock quant without duplicates
    """
    # Find product and location
    product = env['product.product'].search([('name', '=', product_name)])
    location = env['stock.location'].search([('name', '=', location_name)])
    
    if not product or not location:
        print(f"Product or location not found: {product_name}, {location_name}")
        return False
    
    # Check if quant already exists
    existing_quant = env['stock.quant'].search([
        ('product_id', '=', product.id),
        ('location_id', '=', location.id)
    ], limit=1)
    
    if existing_quant:
        # Update existing quant
        existing_quant.inventory_quantity = quantity
        existing_quant.action_apply_inventory()
        print(f"Updated existing quant: {product_name} in {location_name} to {quantity}")
    else:
        # Create new quant
        env['stock.quant'].create({
            'product_id': product.id,
            'location_id': location.id,
            'quantity': quantity,
        })
        print(f"Created new quant: {product_name} in {location_name} with {quantity}")
    
    return True

# Usage example:
# safe_create_or_update_quant(env, 'VARIYALI READY DIAMOND 1% [30KG]', 'Stock', 100.0)
'''
    
    print(python_code)

def solution_5_bypass_constraint():
    """
    Solution 5: Temporarily bypass constraint if needed
    """
    print("=== SOLUTION 5: BYPASS CONSTRAINT (USE WITH CAUTION) ===")
    print()
    print("Only use this if you're sure the constraint is incorrect:")
    print()
    
    odoo_shell_code = '''
# Run in Odoo shell to bypass constraint temporarily

# Method A: Use SQL directly (be very careful!)
env.cr.execute("""
    INSERT INTO stock_quant (product_id, location_id, quantity, company_id, create_date, write_date, create_uid, write_uid)
    SELECT %s, %s, %s, %s, NOW(), NOW(), %s, %s
    WHERE NOT EXISTS (
        SELECT 1 FROM stock_quant 
        WHERE product_id = %s AND location_id = %s
    )
""", [product.id, stock_location.id, quantity, env.company.id, env.uid, env.uid, product.id, stock_location.id])

# Method B: Use with_context to skip validations (if supported)
env['stock.quant'].with_context(skip_validation=True).create({
    'product_id': product.id,
    'location_id': stock_location.id,
    'quantity': quantity,
})

# Method C: Disable constraint temporarily (advanced)
# This requires modifying the model temporarily
'''
    
    print(odoo_shell_code)

if __name__ == "__main__":
    print("SOLUTIONS FOR DUPLICATE PRODUCT-LOCATION ERROR")
    print("=" * 50)
    print()
    
    solution_1_inventory_adjustment()
    print("\n" + "="*50 + "\n")
    
    solution_2_update_existing_quant()
    print("\n" + "="*50 + "\n")
    
    solution_3_check_constraints()
    print("\n" + "="*50 + "\n")
    
    solution_4_data_import_fix()
    print("\n" + "="*50 + "\n")
    
    solution_5_bypass_constraint()
    print("\n" + "="*50 + "\n")
    
    print("RECOMMENDATION:")
    print("1. Start with Solution 1 or 2 (safest)")
    print("2. Use Solution 3 to understand the constraint")
    print("3. Use Solution 4 for data imports")
    print("4. Only use Solution 5 as a last resort")
